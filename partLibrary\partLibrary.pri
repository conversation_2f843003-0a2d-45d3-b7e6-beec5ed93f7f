HEADERS += \
    $$PWD/PartLibraryManager.h \
    $$PWD/database/SimpleDbManager.h \
    $$PWD/database/globalDb/sqlCtrl/attributedefinitionsctrl.h \
    $$PWD/database/globalDb/sqlCtrl/categoriesctrl.h \
    $$PWD/database/globalDb/sqlCtrl/categoryattributelinksctrl.h \
    $$PWD/database/globalDb/sqlCtrl/manufacturerctrl.h \
    $$PWD/database/globalDb/sqlModel/attributedefinitionsmodel.h \
    $$PWD/database/globalDb/sqlModel/categoriesmodel.h \
    $$PWD/database/globalDb/sqlModel/categoryattributelinksmodel.h \
    $$PWD/database/globalDb/sqlModel/manufacturermodel.h \
    $$PWD/database/partDb/sqlCtrl/changeLogCtrl.h \
    $$PWD/database/partDb/sqlCtrl/datasheetsctrl.h \
    $$PWD/database/partDb/sqlCtrl/libraryinfoctrl.h \
    $$PWD/database/partDb/sqlCtrl/partImagesCtrl.h \
    $$PWD/database/partDb/sqlCtrl/partctrl.h \
    $$PWD/database/partDb/sqlCtrl/partdatasheetlinksctrl.h \
    $$PWD/database/partDb/sqlModel/changeLogModel.h \
    $$PWD/database/partDb/sqlModel/datasheetsmodel.h \
    $$PWD/database/partDb/sqlModel/libraryinfomodel.h \
    $$PWD/database/partDb/sqlModel/partImageModel.h \
    $$PWD/database/partDb/sqlModel/partdatasheetlinksmodel.h \
    $$PWD/database/partDb/sqlModel/partmodel.h \
    $$PWD/database/queryprofiler.h \
    $$PWD/database/sqlCtrlBase.h \
    $$PWD/database/sqlModelBase.h \
    $$PWD/database/viewModel/CategoriesViewModel.h \
    $$PWD/database/viewModel/PartViewModel.h \
    $$PWD/database/viewModel/ViewModelAssembler.h \
    $$PWD/tests/TestTracker.h \
    $$PWD/tests/test_globaldb.h \
    $$PWD/tests/test_partdb.h

SOURCES += \
    $$PWD/PartLibraryManager.cpp \
    $$PWD/database/SimpleDbManager.cpp \
    $$PWD/database/globalDb/sqlCtrl/attributedefinitionsctrl.cpp \
    $$PWD/database/globalDb/sqlCtrl/categoriesctrl.cpp \
    $$PWD/database/globalDb/sqlCtrl/categoryattributelinksctrl.cpp \
    $$PWD/database/globalDb/sqlCtrl/manufacturerctrl.cpp \
    $$PWD/database/globalDb/sqlModel/attributedefinitionsmodel.cpp \
    $$PWD/database/globalDb/sqlModel/categoriesmodel.cpp \
    $$PWD/database/globalDb/sqlModel/categoryattributelinksmodel.cpp \
    $$PWD/database/globalDb/sqlModel/manufacturermodel.cpp \
    $$PWD/database/partDb/sqlCtrl/changeLogCtrl.cpp \
    $$PWD/database/partDb/sqlCtrl/datasheetsctrl.cpp \
    $$PWD/database/partDb/sqlCtrl/libraryinfoctrl.cpp \
    $$PWD/database/partDb/sqlCtrl/partImagesCtrl.cpp \
    $$PWD/database/partDb/sqlCtrl/partctrl.cpp \
    $$PWD/database/partDb/sqlCtrl/partdatasheetlinksctrl.cpp \
    $$PWD/database/partDb/sqlModel/changeLogModel.cpp \
    $$PWD/database/partDb/sqlModel/datasheetsmodel.cpp \
    $$PWD/database/partDb/sqlModel/libraryinfomodel.cpp \
    $$PWD/database/partDb/sqlModel/partImageModel.cpp \
    $$PWD/database/partDb/sqlModel/partdatasheetlinksmodel.cpp \
    $$PWD/database/partDb/sqlModel/partmodel.cpp \
    $$PWD/database/queryprofiler.cpp \
    $$PWD/database/sqlCtrlBase.cpp \
    $$PWD/database/sqlModelBase.cpp \
    $$PWD/database/viewModel/ViewModelAssembler.cpp \
    $$PWD/tests/test_globaldb.cpp \
    $$PWD/tests/test_partdb.cpp

